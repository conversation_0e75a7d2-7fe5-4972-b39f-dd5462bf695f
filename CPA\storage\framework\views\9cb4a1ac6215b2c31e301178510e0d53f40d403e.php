

<?php $__env->startSection('content'); ?>
    <div class="page-content login-cover">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Content area -->
            <div class="content d-flex justify-content-center align-items-center">

                <!-- Login card -->
                <form class="login-form " method="post" action="<?php echo e(route('login')); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="card mb-0">
                        <div class="card-body" >
                            <div class="text-center mb-3">
        
                                <img src="/images/logo_avar.png" alt="Logo Avara" style="width: 300px;
                                height: auto;
                                position: relative;
                                margin-left: -9px;" class="icon-2x text-warning-400 p-3 mb-3 mt-1 logos">
                                <h5 class="mb-0">Connectez - vous</h5>
                                <span class="d-block text-muted">Votre Email et Mot de passe</span>
                            </div>

                                <?php if($errors->any()): ?>
                                <div class="alert alert-danger alert-styled-left alert-dismissible">
                                    <button type="button" class="close" data-dismiss="alert"><span>&times;</span></button>
                                    <span class="font-weight-semibold">Oops!</span> <?php echo e(implode('<br>', $errors->all())); ?>

                                </div>
                                <?php endif; ?>


                            <div class="form-group ">
                                <input type="text" class="form-control" name="identity" value="<?php echo e(old('identity')); ?>" placeholder="Votre Email ou Nom d'utilisateur">
                            </div>

                            <div class="form-group ">
                                <input required name="password" type="password" class="form-control" placeholder="<?php echo e(__('Mot de passe')); ?>">

                            </div>

                            <div class="form-group d-flex align-items-center">
                                <div class="form-check mb-0">
                                    <label class="form-check-label">
                                        <input type="checkbox" name="remember" class="form-input-styled" <?php echo e(old('remember') ? 'checked' : ''); ?> data-fouc>
                                        enregistrement prochaine session
                                    </label>
                                </div>

                                <a href="<?php echo e(route('password.request')); ?>" class="ml-auto">Mot de passe Oublier?</a>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary btn-block">Connectez <i class="icon-circle-right2 ml-2"></i></button>
                            </div>

                           


                        </div>
                    </div>
                </form>

            </div>


        </div>

    </div>
    <?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.login_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\dev\CPA\CPA\resources\views/auth/login.blade.php ENDPATH**/ ?>