<?php
/**
 * Script de test pour vérifier que l'application Laravel fonctionne
 */

// Activer l'affichage des erreurs
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "=== Test de l'application Laravel ===\n\n";

// Test 1: Vérifier l'autoloader
echo "1. Test de l'autoloader... ";
try {
    require __DIR__.'/vendor/autoload.php';
    echo "✓ OK\n";
} catch (Exception $e) {
    echo "✗ ERREUR: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Vérifier le bootstrap
echo "2. Test du bootstrap... ";
try {
    $app = require_once __DIR__.'/bootstrap/app.php';
    echo "✓ OK\n";
} catch (Exception $e) {
    echo "✗ ERREUR: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 3: Vérifier le kernel
echo "3. Test du kernel... ";
try {
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    echo "✓ OK\n";
} catch (Exception $e) {
    echo "✗ ERREUR: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 4: Vérifier la configuration
echo "4. Test de la configuration... ";
try {
    // Initialiser l'application
    $app->bootstrapWith([
        \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
        \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
    ]);

    $config = $app->make('config');
    $appName = $config->get('app.name', 'Laravel');
    echo "✓ OK (App: $appName)\n";
} catch (Exception $e) {
    echo "✗ ERREUR: " . $e->getMessage() . "\n";
    // Ne pas quitter, continuer les tests
}

// Test 5: Vérifier les routes
echo "5. Test des routes... ";
try {
    $router = $app->make('router');
    echo "✓ OK\n";
} catch (Exception $e) {
    echo "✗ ERREUR: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Tous les tests sont passés avec succès ! ===\n";
echo "L'application Laravel est prête à être utilisée.\n";
echo "Vous pouvez maintenant démarrer le serveur avec: php start_server.php\n";
