<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'App\\Console\\Kernel' => $baseDir . '/app/Console/Kernel.php',
    'App\\Exceptions\\Handler' => $baseDir . '/app/Exceptions/Handler.php',
    'App\\Helpers\\Mk' => $baseDir . '/app/Helpers/Mk.php',
    'App\\Helpers\\Pay' => $baseDir . '/app/Helpers/Pay.php',
    'App\\Helpers\\Qs' => $baseDir . '/app/Helpers/Qs.php',
    'App\\Http\\Controllers\\AjaxController' => $baseDir . '/app/Http/Controllers/AjaxController.php',
    'App\\Http\\Controllers\\Auth\\ForgotPasswordController' => $baseDir . '/app/Http/Controllers/Auth/ForgotPasswordController.php',
    'App\\Http\\Controllers\\Auth\\LoginController' => $baseDir . '/app/Http/Controllers/Auth/LoginController.php',
    'App\\Http\\Controllers\\Auth\\RegisterController' => $baseDir . '/app/Http/Controllers/Auth/RegisterController.php',
    'App\\Http\\Controllers\\Auth\\ResetPasswordController' => $baseDir . '/app/Http/Controllers/Auth/ResetPasswordController.php',
    'App\\Http\\Controllers\\Auth\\VerificationController' => $baseDir . '/app/Http/Controllers/Auth/VerificationController.php',
    'App\\Http\\Controllers\\Controller' => $baseDir . '/app/Http/Controllers/Controller.php',
    'App\\Http\\Controllers\\HomeController' => $baseDir . '/app/Http/Controllers/HomeController.php',
    'App\\Http\\Controllers\\MyAccountController' => $baseDir . '/app/Http/Controllers/MyAccountController.php',
    'App\\Http\\Controllers\\MyParent\\MyController' => $baseDir . '/app/Http/Controllers/MyParent/MyController.php',
    'App\\Http\\Controllers\\SuperAdmin\\SettingController' => $baseDir . '/app/Http/Controllers/SuperAdmin/SettingController.php',
    'App\\Http\\Controllers\\SupportTeam\\DormController' => $baseDir . '/app/Http/Controllers/SupportTeam/DormController.php',
    'App\\Http\\Controllers\\SupportTeam\\ExamController' => $baseDir . '/app/Http/Controllers/SupportTeam/ExamController.php',
    'App\\Http\\Controllers\\SupportTeam\\GradeController' => $baseDir . '/app/Http/Controllers/SupportTeam/GradeController.php',
    'App\\Http\\Controllers\\SupportTeam\\MarkController' => $baseDir . '/app/Http/Controllers/SupportTeam/MarkController.php',
    'App\\Http\\Controllers\\SupportTeam\\MyClassController' => $baseDir . '/app/Http/Controllers/SupportTeam/MyClassController.php',
    'App\\Http\\Controllers\\SupportTeam\\PaymentController' => $baseDir . '/app/Http/Controllers/SupportTeam/PaymentController.php',
    'App\\Http\\Controllers\\SupportTeam\\PinController' => $baseDir . '/app/Http/Controllers/SupportTeam/PinController.php',
    'App\\Http\\Controllers\\SupportTeam\\PromotionController' => $baseDir . '/app/Http/Controllers/SupportTeam/PromotionController.php',
    'App\\Http\\Controllers\\SupportTeam\\SectionController' => $baseDir . '/app/Http/Controllers/SupportTeam/SectionController.php',
    'App\\Http\\Controllers\\SupportTeam\\StudentRecordController' => $baseDir . '/app/Http/Controllers/SupportTeam/StudentRecordController.php',
    'App\\Http\\Controllers\\SupportTeam\\SubjectController' => $baseDir . '/app/Http/Controllers/SupportTeam/SubjectController.php',
    'App\\Http\\Controllers\\SupportTeam\\TimeTableController' => $baseDir . '/app/Http/Controllers/SupportTeam/TimeTableController.php',
    'App\\Http\\Controllers\\SupportTeam\\UserController' => $baseDir . '/app/Http/Controllers/SupportTeam/UserController.php',
    'App\\Http\\Controllers\\TestController' => $baseDir . '/app/Http/Controllers/TestController.php',
    'App\\Http\\Kernel' => $baseDir . '/app/Http/Kernel.php',
    'App\\Http\\Middleware\\Authenticate' => $baseDir . '/app/Http/Middleware/Authenticate.php',
    'App\\Http\\Middleware\\CheckForMaintenanceMode' => $baseDir . '/app/Http/Middleware/CheckForMaintenanceMode.php',
    'App\\Http\\Middleware\\Custom\\Admin' => $baseDir . '/app/Http/Middleware/Custom/Admin.php',
    'App\\Http\\Middleware\\Custom\\ExamIsLocked' => $baseDir . '/app/Http/Middleware/Custom/ExamIsLocked.php',
    'App\\Http\\Middleware\\Custom\\MyParent' => $baseDir . '/app/Http/Middleware/Custom/MyParent.php',
    'App\\Http\\Middleware\\Custom\\Student' => $baseDir . '/app/Http/Middleware/Custom/Student.php',
    'App\\Http\\Middleware\\Custom\\SuperAdmin' => $baseDir . '/app/Http/Middleware/Custom/SuperAdmin.php',
    'App\\Http\\Middleware\\Custom\\Teacher' => $baseDir . '/app/Http/Middleware/Custom/Teacher.php',
    'App\\Http\\Middleware\\Custom\\TeamAccount' => $baseDir . '/app/Http/Middleware/Custom/TeamAccount.php',
    'App\\Http\\Middleware\\Custom\\TeamSA' => $baseDir . '/app/Http/Middleware/Custom/TeamSA.php',
    'App\\Http\\Middleware\\Custom\\TeamSAT' => $baseDir . '/app/Http/Middleware/Custom/TeamSAT.php',
    'App\\Http\\Middleware\\EncryptCookies' => $baseDir . '/app/Http/Middleware/EncryptCookies.php',
    'App\\Http\\Middleware\\PreventRequestsDuringMaintenance' => $baseDir . '/app/Http/Middleware/PreventRequestsDuringMaintenance.php',
    'App\\Http\\Middleware\\RedirectIfAuthenticated' => $baseDir . '/app/Http/Middleware/RedirectIfAuthenticated.php',
    'App\\Http\\Middleware\\TrimStrings' => $baseDir . '/app/Http/Middleware/TrimStrings.php',
    'App\\Http\\Middleware\\TrustProxies' => $baseDir . '/app/Http/Middleware/TrustProxies.php',
    'App\\Http\\Middleware\\VerifyCsrfToken' => $baseDir . '/app/Http/Middleware/VerifyCsrfToken.php',
    'App\\Http\\Requests\\Dorm\\DormCreate' => $baseDir . '/app/Http/Requests/Dorm/DormCreate.php',
    'App\\Http\\Requests\\Dorm\\DormUpdate' => $baseDir . '/app/Http/Requests/Dorm/DormUpdate.php',
    'App\\Http\\Requests\\Exam\\ExamCreate' => $baseDir . '/app/Http/Requests/Exam/ExamCreate.php',
    'App\\Http\\Requests\\Exam\\ExamUpdate' => $baseDir . '/app/Http/Requests/Exam/ExamUpdate.php',
    'App\\Http\\Requests\\Grade\\GradeCreate' => $baseDir . '/app/Http/Requests/Grade/GradeCreate.php',
    'App\\Http\\Requests\\Grade\\GradeUpdate' => $baseDir . '/app/Http/Requests/Grade/GradeUpdate.php',
    'App\\Http\\Requests\\Mark\\MarkSelector' => $baseDir . '/app/Http/Requests/Mark/MarkSelector.php',
    'App\\Http\\Requests\\Mark\\MarkUpdate' => $baseDir . '/app/Http/Requests/Mark/MarkUpdate.php',
    'App\\Http\\Requests\\MyClass\\ClassCreate' => $baseDir . '/app/Http/Requests/MyClass/ClassCreate.php',
    'App\\Http\\Requests\\MyClass\\ClassUpdate' => $baseDir . '/app/Http/Requests/MyClass/ClassUpdate.php',
    'App\\Http\\Requests\\Payment\\PaymentCreate' => $baseDir . '/app/Http/Requests/Payment/PaymentCreate.php',
    'App\\Http\\Requests\\Payment\\PaymentUpdate' => $baseDir . '/app/Http/Requests/Payment/PaymentUpdate.php',
    'App\\Http\\Requests\\Pin\\PinCreate' => $baseDir . '/app/Http/Requests/Pin/PinCreate.php',
    'App\\Http\\Requests\\Pin\\PinVerify' => $baseDir . '/app/Http/Requests/Pin/PinVerify.php',
    'App\\Http\\Requests\\Section\\SectionCreate' => $baseDir . '/app/Http/Requests/Section/SectionCreate.php',
    'App\\Http\\Requests\\Section\\SectionUpdate' => $baseDir . '/app/Http/Requests/Section/SectionUpdate.php',
    'App\\Http\\Requests\\SettingUpdate' => $baseDir . '/app/Http/Requests/SettingUpdate.php',
    'App\\Http\\Requests\\Student\\StudentRecordCreate' => $baseDir . '/app/Http/Requests/Student/StudentRecordCreate.php',
    'App\\Http\\Requests\\Student\\StudentRecordUpdate' => $baseDir . '/app/Http/Requests/Student/StudentRecordUpdate.php',
    'App\\Http\\Requests\\Subject\\SubjectCreate' => $baseDir . '/app/Http/Requests/Subject/SubjectCreate.php',
    'App\\Http\\Requests\\Subject\\SubjectUpdate' => $baseDir . '/app/Http/Requests/Subject/SubjectUpdate.php',
    'App\\Http\\Requests\\TimeTable\\TSRequest' => $baseDir . '/app/Http/Requests/TimeTable/TSRequest.php',
    'App\\Http\\Requests\\TimeTable\\TTRecordRequest' => $baseDir . '/app/Http/Requests/TimeTable/TTRecordRequest.php',
    'App\\Http\\Requests\\TimeTable\\TTRequest' => $baseDir . '/app/Http/Requests/TimeTable/TTRequest.php',
    'App\\Http\\Requests\\UserChangePass' => $baseDir . '/app/Http/Requests/UserChangePass.php',
    'App\\Http\\Requests\\UserRequest' => $baseDir . '/app/Http/Requests/UserRequest.php',
    'App\\Http\\Requests\\UserUpdate' => $baseDir . '/app/Http/Requests/UserUpdate.php',
    'App\\Models\\BloodGroup' => $baseDir . '/app/Models/BloodGroup.php',
    'App\\Models\\ClassType' => $baseDir . '/app/Models/ClassType.php',
    'App\\Models\\Dorm' => $baseDir . '/app/Models/Dorm.php',
    'App\\Models\\Exam' => $baseDir . '/app/Models/Exam.php',
    'App\\Models\\ExamRecord' => $baseDir . '/app/Models/ExamRecord.php',
    'App\\Models\\Grade' => $baseDir . '/app/Models/Grade.php',
    'App\\Models\\Lga' => $baseDir . '/app/Models/Lga.php',
    'App\\Models\\Mark' => $baseDir . '/app/Models/Mark.php',
    'App\\Models\\MyClass' => $baseDir . '/app/Models/MyClass.php',
    'App\\Models\\Nationality' => $baseDir . '/app/Models/Nationality.php',
    'App\\Models\\Payment' => $baseDir . '/app/Models/Payment.php',
    'App\\Models\\PaymentRecord' => $baseDir . '/app/Models/PaymentRecord.php',
    'App\\Models\\Pin' => $baseDir . '/app/Models/Pin.php',
    'App\\Models\\Promotion' => $baseDir . '/app/Models/Promotion.php',
    'App\\Models\\Receipt' => $baseDir . '/app/Models/Receipt.php',
    'App\\Models\\Section' => $baseDir . '/app/Models/Section.php',
    'App\\Models\\Setting' => $baseDir . '/app/Models/Setting.php',
    'App\\Models\\Skill' => $baseDir . '/app/Models/Skill.php',
    'App\\Models\\StaffRecord' => $baseDir . '/app/Models/StaffRecord.php',
    'App\\Models\\State' => $baseDir . '/app/Models/State.php',
    'App\\Models\\StudentRecord' => $baseDir . '/app/Models/StudentRecord.php',
    'App\\Models\\Subject' => $baseDir . '/app/Models/Subject.php',
    'App\\Models\\TimeSlot' => $baseDir . '/app/Models/TimeSlot.php',
    'App\\Models\\TimeTable' => $baseDir . '/app/Models/TimeTable.php',
    'App\\Models\\TimeTableRecord' => $baseDir . '/app/Models/TimeTableRecord.php',
    'App\\Models\\UserType' => $baseDir . '/app/Models/UserType.php',
    'App\\Models\\decaissement' => $baseDir . '/app/Models/decaissement.php',
    'App\\Providers\\AppServiceProvider' => $baseDir . '/app/Providers/AppServiceProvider.php',
    'App\\Providers\\AuthServiceProvider' => $baseDir . '/app/Providers/AuthServiceProvider.php',
    'App\\Providers\\BroadcastServiceProvider' => $baseDir . '/app/Providers/BroadcastServiceProvider.php',
    'App\\Providers\\EventServiceProvider' => $baseDir . '/app/Providers/EventServiceProvider.php',
    'App\\Providers\\RouteServiceProvider' => $baseDir . '/app/Providers/RouteServiceProvider.php',
    'App\\Repositories\\DormRepo' => $baseDir . '/app/Repositories/DormRepo.php',
    'App\\Repositories\\ExamRepo' => $baseDir . '/app/Repositories/ExamRepo.php',
    'App\\Repositories\\LocationRepo' => $baseDir . '/app/Repositories/LocationRepo.php',
    'App\\Repositories\\MarkRepo' => $baseDir . '/app/Repositories/MarkRepo.php',
    'App\\Repositories\\MyClassRepo' => $baseDir . '/app/Repositories/MyClassRepo.php',
    'App\\Repositories\\PaymentRepo' => $baseDir . '/app/Repositories/PaymentRepo.php',
    'App\\Repositories\\PinRepo' => $baseDir . '/app/Repositories/PinRepo.php',
    'App\\Repositories\\SettingRepo' => $baseDir . '/app/Repositories/SettingRepo.php',
    'App\\Repositories\\StudentRepo' => $baseDir . '/app/Repositories/StudentRepo.php',
    'App\\Repositories\\TimeTableRepo' => $baseDir . '/app/Repositories/TimeTableRepo.php',
    'App\\Repositories\\UserRepo' => $baseDir . '/app/Repositories/UserRepo.php',
    'App\\User' => $baseDir . '/app/User.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Database\\Factories\\StudentRecordFactory' => $baseDir . '/database/factories/StudentRecordFactory.php',
    'Database\\Factories\\UserFactory' => $baseDir . '/database/factories/UserFactory.php',
    'Database\\Seeders\\ClassTypesTableSeeder' => $baseDir . '/database/seeders/ClassTypesTableSeeder.php',
    'Database\\Seeders\\DatabaseSeeder' => $baseDir . '/database/seeders/DatabaseSeeder.php',
    'Database\\Seeders\\SettingsTableSeeder' => $baseDir . '/database/seeders/SettingsTableSeeder.php',
    'Database\\Seeders\\UserTypesTableSeeder' => $baseDir . '/database/seeders/UserTypesTableSeeder.php',
    'Database\\Seeders\\UsersTableSeeder' => $baseDir . '/database/seeders/UsersTableSeeder.php',
);
