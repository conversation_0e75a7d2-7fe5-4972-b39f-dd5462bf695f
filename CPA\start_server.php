<?php
/**
 * Script de démarrage du serveur Laravel sans composer
 */

// Définir le répertoire de travail
$projectDir = __DIR__;
$publicDir = $projectDir . '/public';

// Vérifier que le répertoire public existe
if (!is_dir($publicDir)) {
    die("Erreur: Le répertoire public n'existe pas.\n");
}

// Vérifier que vendor/autoload.php existe
if (!file_exists($projectDir . '/vendor/autoload.php')) {
    die("Erreur: Les dépendances Composer ne sont pas installées.\n");
}

// Vérifier que le fichier .env existe
if (!file_exists($projectDir . '/.env')) {
    die("Erreur: Le fichier .env n'existe pas.\n");
}

echo "Démarrage du serveur Laravel...\n";
echo "URL: http://127.0.0.1:8000\n";
echo "Répertoire public: $publicDir\n";
echo "Appuyez sur Ctrl+C pour arrêter le serveur.\n\n";

// Démarrer le serveur PHP intégré
$command = "php -S 127.0.0.1:8000 -t \"$publicDir\"";
passthru($command);
