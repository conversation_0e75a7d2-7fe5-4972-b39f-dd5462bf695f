<?php

namespace Faker\Provider\hr_HR;

class Person extends \Faker\Provider\Person
{
    /**
     * @see http://www.behindthename.com/top/lists/croatia/2009
     */
    protected static $firstNameMale = [
        '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', 'Vid', 'Viktor', 'Zvonimir', 'Šime', 'Šimun',
    ];

    /**
     * @see http://www.behindthename.com/top/lists/croatia/2009
     */
    protected static $firstNameFemale = [
        'Ana', 'Anamarija', 'Andrea', 'Anja', 'Antonela', 'Antonija', 'Anđela', 'Barbara', 'Dora', 'Dorotea', 'Dunja', 'Ela', 'Elena', 'Ella', 'Ema', 'Ena', 'Eva', 'Franka', 'Gabrijela', 'Hana', 'Helena', 'Ines', 'Iris', 'Ivana', 'Ivona', 'Jelena', 'Josipa', 'Karla', 'Katarina', 'Katja', 'Klara', 'Korina', 'Kristina', 'Lana', 'Lara', 'Laura', 'Lea', 'Lena', 'Leona', 'Lora', 'Lorena', 'Lucija', 'Magdalena', 'Maja', 'Manuela', 'Mara', 'Marija', 'Marina', 'Marta', 'Martina', 'Matea', 'Maša', 'Melani', 'Melanie', 'Mia', 'Mihaela', 'Mila', 'Monika', 'Nela', 'Nika', 'Nikolina', 'Nina', 'Nora', 'Paola', 'Patricia', 'Paula', 'Petra', 'Sara', 'Stela', 'Stella', 'Tamara', 'Tara', 'Tea', 'Tena', 'Tia', 'Tina', 'Valentina', 'Vanesa', 'Vanessa', 'Veronika', 'Viktorija',
    ];

    /**
     * @see http://surnames.behindthename.com/names/usage/croatian
     */
    protected static $lastName = [
        'Abramović', 'Adamić', 'Antić', 'Babić', 'Blažević', 'Bogdanić', 'Bogdanović', 'Božić', 'Brož', 'Dragić', 'Dragović', 'Filipović', 'Franić', 'Franjić', 'Grgić', 'Horvat', 'Horvatinčić', 'Ivanović', 'Janković', 'Jurić', 'Juriša', 'Kasun', 'Knežević', 'Kovač', 'Kovačević', 'Kovačić', 'Košar', 'Kranjčar', 'Lovren', 'Mandžukić', 'Maras', 'Marić', 'Marković', 'Marušić', 'Matić', 'Milić', 'Mlakar', 'Modrić', 'Neretljak', 'Nikolić', 'Novak', 'Novaković', 'Pavić', 'Pavletić', 'Perić', 'Perković', 'Petrović', 'Radić', 'Raić-Sudar', 'Ratković', 'Srna', 'Stanković', 'Tomić', 'Tomčić', 'Vincetić', 'Vinković', 'Vlahović', 'Vlašić', 'Vuka', 'Vuković', 'Zorić', 'Ćorluka', 'Čupić', 'Župan',
    ];
}
