<?php

/**
 * <PERSON><PERSON> - A PHP Framework For Web Artisans
 *
 * @package  <PERSON><PERSON>
 * <AUTHOR> <<EMAIL>>
 */

// Activer l'affichage des erreurs pour le débogage
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

define('LARAVEL_START', microtime(true));

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
|
| Composer provides a convenient, automatically generated class loader for
| our application. We just need to utilize it! We'll simply require it
| into the script here so that we don't have to worry about manual
| loading any of our classes later on. It feels great to relax.
|
*/

try {
    require __DIR__.'/../vendor/autoload.php';
} catch (Exception $e) {
    die('Erreur lors du chargement de l\'autoloader: ' . $e->getMessage());
}

/*
|--------------------------------------------------------------------------
| Turn On The Lights
|--------------------------------------------------------------------------
|
| We need to illuminate PHP development, so let us turn on the lights.
| This bootstraps the framework and gets it ready for use, then it
| will load up this application so that we can run it and send
| the responses back to the browser and delight our users.
|
*/

try {
    $app = require_once __DIR__.'/../bootstrap/app.php';
} catch (Exception $e) {
    die('Erreur lors du chargement de l\'application: ' . $e->getMessage());
}

/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
|
| Once we have the application, we can handle the incoming request
| through the kernel, and send the associated response back to
| the client's browser allowing them to enjoy the creative
| and wonderful application we have prepared for them.
|
*/

try {
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

    $response = $kernel->handle(
        $request = Illuminate\Http\Request::capture()
    );

    $response->send();

    $kernel->terminate($request, $response);
} catch (Exception $e) {
    die('Erreur lors de l\'exécution de l\'application: ' . $e->getMessage() . '<br><br>Stack trace:<br>' . nl2br($e->getTraceAsString()));
} catch (Error $e) {
    die('Erreur fatale: ' . $e->getMessage() . '<br><br>Stack trace:<br>' . nl2br($e->getTraceAsString()));
}
