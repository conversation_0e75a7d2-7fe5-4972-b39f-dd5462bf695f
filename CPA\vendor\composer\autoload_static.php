<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitea995b8cb853bf0aff0cfe4706707e10
{
    public static $prefixLengthsPsr4 = array (
        'D' => 
        array (
            'Database\\Seeders\\' => 17,
            'Database\\Factories\\' => 19,
        ),
        'A' => 
        array (
            'App\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Database\\Seeders\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/seeders',
        ),
        'Database\\Factories\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/factories',
        ),
        'App\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $classMap = array (
        'App\\Console\\Kernel' => __DIR__ . '/../..' . '/app/Console/Kernel.php',
        'App\\Exceptions\\Handler' => __DIR__ . '/../..' . '/app/Exceptions/Handler.php',
        'App\\Helpers\\Mk' => __DIR__ . '/../..' . '/app/Helpers/Mk.php',
        'App\\Helpers\\Pay' => __DIR__ . '/../..' . '/app/Helpers/Pay.php',
        'App\\Helpers\\Qs' => __DIR__ . '/../..' . '/app/Helpers/Qs.php',
        'App\\Http\\Controllers\\AjaxController' => __DIR__ . '/../..' . '/app/Http/Controllers/AjaxController.php',
        'App\\Http\\Controllers\\Auth\\ForgotPasswordController' => __DIR__ . '/../..' . '/app/Http/Controllers/Auth/ForgotPasswordController.php',
        'App\\Http\\Controllers\\Auth\\LoginController' => __DIR__ . '/../..' . '/app/Http/Controllers/Auth/LoginController.php',
        'App\\Http\\Controllers\\Auth\\RegisterController' => __DIR__ . '/../..' . '/app/Http/Controllers/Auth/RegisterController.php',
        'App\\Http\\Controllers\\Auth\\ResetPasswordController' => __DIR__ . '/../..' . '/app/Http/Controllers/Auth/ResetPasswordController.php',
        'App\\Http\\Controllers\\Auth\\VerificationController' => __DIR__ . '/../..' . '/app/Http/Controllers/Auth/VerificationController.php',
        'App\\Http\\Controllers\\Controller' => __DIR__ . '/../..' . '/app/Http/Controllers/Controller.php',
        'App\\Http\\Controllers\\HomeController' => __DIR__ . '/../..' . '/app/Http/Controllers/HomeController.php',
        'App\\Http\\Controllers\\MyAccountController' => __DIR__ . '/../..' . '/app/Http/Controllers/MyAccountController.php',
        'App\\Http\\Controllers\\MyParent\\MyController' => __DIR__ . '/../..' . '/app/Http/Controllers/MyParent/MyController.php',
        'App\\Http\\Controllers\\SuperAdmin\\SettingController' => __DIR__ . '/../..' . '/app/Http/Controllers/SuperAdmin/SettingController.php',
        'App\\Http\\Controllers\\SupportTeam\\DormController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/DormController.php',
        'App\\Http\\Controllers\\SupportTeam\\ExamController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/ExamController.php',
        'App\\Http\\Controllers\\SupportTeam\\GradeController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/GradeController.php',
        'App\\Http\\Controllers\\SupportTeam\\MarkController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/MarkController.php',
        'App\\Http\\Controllers\\SupportTeam\\MyClassController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/MyClassController.php',
        'App\\Http\\Controllers\\SupportTeam\\PaymentController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/PaymentController.php',
        'App\\Http\\Controllers\\SupportTeam\\PinController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/PinController.php',
        'App\\Http\\Controllers\\SupportTeam\\PromotionController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/PromotionController.php',
        'App\\Http\\Controllers\\SupportTeam\\SectionController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/SectionController.php',
        'App\\Http\\Controllers\\SupportTeam\\StudentRecordController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/StudentRecordController.php',
        'App\\Http\\Controllers\\SupportTeam\\SubjectController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/SubjectController.php',
        'App\\Http\\Controllers\\SupportTeam\\TimeTableController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/TimeTableController.php',
        'App\\Http\\Controllers\\SupportTeam\\UserController' => __DIR__ . '/../..' . '/app/Http/Controllers/SupportTeam/UserController.php',
        'App\\Http\\Controllers\\TestController' => __DIR__ . '/../..' . '/app/Http/Controllers/TestController.php',
        'App\\Http\\Kernel' => __DIR__ . '/../..' . '/app/Http/Kernel.php',
        'App\\Http\\Middleware\\Authenticate' => __DIR__ . '/../..' . '/app/Http/Middleware/Authenticate.php',
        'App\\Http\\Middleware\\CheckForMaintenanceMode' => __DIR__ . '/../..' . '/app/Http/Middleware/CheckForMaintenanceMode.php',
        'App\\Http\\Middleware\\Custom\\Admin' => __DIR__ . '/../..' . '/app/Http/Middleware/Custom/Admin.php',
        'App\\Http\\Middleware\\Custom\\ExamIsLocked' => __DIR__ . '/../..' . '/app/Http/Middleware/Custom/ExamIsLocked.php',
        'App\\Http\\Middleware\\Custom\\MyParent' => __DIR__ . '/../..' . '/app/Http/Middleware/Custom/MyParent.php',
        'App\\Http\\Middleware\\Custom\\Student' => __DIR__ . '/../..' . '/app/Http/Middleware/Custom/Student.php',
        'App\\Http\\Middleware\\Custom\\SuperAdmin' => __DIR__ . '/../..' . '/app/Http/Middleware/Custom/SuperAdmin.php',
        'App\\Http\\Middleware\\Custom\\Teacher' => __DIR__ . '/../..' . '/app/Http/Middleware/Custom/Teacher.php',
        'App\\Http\\Middleware\\Custom\\TeamAccount' => __DIR__ . '/../..' . '/app/Http/Middleware/Custom/TeamAccount.php',
        'App\\Http\\Middleware\\Custom\\TeamSA' => __DIR__ . '/../..' . '/app/Http/Middleware/Custom/TeamSA.php',
        'App\\Http\\Middleware\\Custom\\TeamSAT' => __DIR__ . '/../..' . '/app/Http/Middleware/Custom/TeamSAT.php',
        'App\\Http\\Middleware\\EncryptCookies' => __DIR__ . '/../..' . '/app/Http/Middleware/EncryptCookies.php',
        'App\\Http\\Middleware\\PreventRequestsDuringMaintenance' => __DIR__ . '/../..' . '/app/Http/Middleware/PreventRequestsDuringMaintenance.php',
        'App\\Http\\Middleware\\RedirectIfAuthenticated' => __DIR__ . '/../..' . '/app/Http/Middleware/RedirectIfAuthenticated.php',
        'App\\Http\\Middleware\\TrimStrings' => __DIR__ . '/../..' . '/app/Http/Middleware/TrimStrings.php',
        'App\\Http\\Middleware\\TrustProxies' => __DIR__ . '/../..' . '/app/Http/Middleware/TrustProxies.php',
        'App\\Http\\Middleware\\VerifyCsrfToken' => __DIR__ . '/../..' . '/app/Http/Middleware/VerifyCsrfToken.php',
        'App\\Http\\Requests\\Dorm\\DormCreate' => __DIR__ . '/../..' . '/app/Http/Requests/Dorm/DormCreate.php',
        'App\\Http\\Requests\\Dorm\\DormUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/Dorm/DormUpdate.php',
        'App\\Http\\Requests\\Exam\\ExamCreate' => __DIR__ . '/../..' . '/app/Http/Requests/Exam/ExamCreate.php',
        'App\\Http\\Requests\\Exam\\ExamUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/Exam/ExamUpdate.php',
        'App\\Http\\Requests\\Grade\\GradeCreate' => __DIR__ . '/../..' . '/app/Http/Requests/Grade/GradeCreate.php',
        'App\\Http\\Requests\\Grade\\GradeUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/Grade/GradeUpdate.php',
        'App\\Http\\Requests\\Mark\\MarkSelector' => __DIR__ . '/../..' . '/app/Http/Requests/Mark/MarkSelector.php',
        'App\\Http\\Requests\\Mark\\MarkUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/Mark/MarkUpdate.php',
        'App\\Http\\Requests\\MyClass\\ClassCreate' => __DIR__ . '/../..' . '/app/Http/Requests/MyClass/ClassCreate.php',
        'App\\Http\\Requests\\MyClass\\ClassUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/MyClass/ClassUpdate.php',
        'App\\Http\\Requests\\Payment\\PaymentCreate' => __DIR__ . '/../..' . '/app/Http/Requests/Payment/PaymentCreate.php',
        'App\\Http\\Requests\\Payment\\PaymentUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/Payment/PaymentUpdate.php',
        'App\\Http\\Requests\\Pin\\PinCreate' => __DIR__ . '/../..' . '/app/Http/Requests/Pin/PinCreate.php',
        'App\\Http\\Requests\\Pin\\PinVerify' => __DIR__ . '/../..' . '/app/Http/Requests/Pin/PinVerify.php',
        'App\\Http\\Requests\\Section\\SectionCreate' => __DIR__ . '/../..' . '/app/Http/Requests/Section/SectionCreate.php',
        'App\\Http\\Requests\\Section\\SectionUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/Section/SectionUpdate.php',
        'App\\Http\\Requests\\SettingUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/SettingUpdate.php',
        'App\\Http\\Requests\\Student\\StudentRecordCreate' => __DIR__ . '/../..' . '/app/Http/Requests/Student/StudentRecordCreate.php',
        'App\\Http\\Requests\\Student\\StudentRecordUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/Student/StudentRecordUpdate.php',
        'App\\Http\\Requests\\Subject\\SubjectCreate' => __DIR__ . '/../..' . '/app/Http/Requests/Subject/SubjectCreate.php',
        'App\\Http\\Requests\\Subject\\SubjectUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/Subject/SubjectUpdate.php',
        'App\\Http\\Requests\\TimeTable\\TSRequest' => __DIR__ . '/../..' . '/app/Http/Requests/TimeTable/TSRequest.php',
        'App\\Http\\Requests\\TimeTable\\TTRecordRequest' => __DIR__ . '/../..' . '/app/Http/Requests/TimeTable/TTRecordRequest.php',
        'App\\Http\\Requests\\TimeTable\\TTRequest' => __DIR__ . '/../..' . '/app/Http/Requests/TimeTable/TTRequest.php',
        'App\\Http\\Requests\\UserChangePass' => __DIR__ . '/../..' . '/app/Http/Requests/UserChangePass.php',
        'App\\Http\\Requests\\UserRequest' => __DIR__ . '/../..' . '/app/Http/Requests/UserRequest.php',
        'App\\Http\\Requests\\UserUpdate' => __DIR__ . '/../..' . '/app/Http/Requests/UserUpdate.php',
        'App\\Models\\BloodGroup' => __DIR__ . '/../..' . '/app/Models/BloodGroup.php',
        'App\\Models\\ClassType' => __DIR__ . '/../..' . '/app/Models/ClassType.php',
        'App\\Models\\Dorm' => __DIR__ . '/../..' . '/app/Models/Dorm.php',
        'App\\Models\\Exam' => __DIR__ . '/../..' . '/app/Models/Exam.php',
        'App\\Models\\ExamRecord' => __DIR__ . '/../..' . '/app/Models/ExamRecord.php',
        'App\\Models\\Grade' => __DIR__ . '/../..' . '/app/Models/Grade.php',
        'App\\Models\\Lga' => __DIR__ . '/../..' . '/app/Models/Lga.php',
        'App\\Models\\Mark' => __DIR__ . '/../..' . '/app/Models/Mark.php',
        'App\\Models\\MyClass' => __DIR__ . '/../..' . '/app/Models/MyClass.php',
        'App\\Models\\Nationality' => __DIR__ . '/../..' . '/app/Models/Nationality.php',
        'App\\Models\\Payment' => __DIR__ . '/../..' . '/app/Models/Payment.php',
        'App\\Models\\PaymentRecord' => __DIR__ . '/../..' . '/app/Models/PaymentRecord.php',
        'App\\Models\\Pin' => __DIR__ . '/../..' . '/app/Models/Pin.php',
        'App\\Models\\Promotion' => __DIR__ . '/../..' . '/app/Models/Promotion.php',
        'App\\Models\\Receipt' => __DIR__ . '/../..' . '/app/Models/Receipt.php',
        'App\\Models\\Section' => __DIR__ . '/../..' . '/app/Models/Section.php',
        'App\\Models\\Setting' => __DIR__ . '/../..' . '/app/Models/Setting.php',
        'App\\Models\\Skill' => __DIR__ . '/../..' . '/app/Models/Skill.php',
        'App\\Models\\StaffRecord' => __DIR__ . '/../..' . '/app/Models/StaffRecord.php',
        'App\\Models\\State' => __DIR__ . '/../..' . '/app/Models/State.php',
        'App\\Models\\StudentRecord' => __DIR__ . '/../..' . '/app/Models/StudentRecord.php',
        'App\\Models\\Subject' => __DIR__ . '/../..' . '/app/Models/Subject.php',
        'App\\Models\\TimeSlot' => __DIR__ . '/../..' . '/app/Models/TimeSlot.php',
        'App\\Models\\TimeTable' => __DIR__ . '/../..' . '/app/Models/TimeTable.php',
        'App\\Models\\TimeTableRecord' => __DIR__ . '/../..' . '/app/Models/TimeTableRecord.php',
        'App\\Models\\UserType' => __DIR__ . '/../..' . '/app/Models/UserType.php',
        'App\\Models\\decaissement' => __DIR__ . '/../..' . '/app/Models/decaissement.php',
        'App\\Providers\\AppServiceProvider' => __DIR__ . '/../..' . '/app/Providers/AppServiceProvider.php',
        'App\\Providers\\AuthServiceProvider' => __DIR__ . '/../..' . '/app/Providers/AuthServiceProvider.php',
        'App\\Providers\\BroadcastServiceProvider' => __DIR__ . '/../..' . '/app/Providers/BroadcastServiceProvider.php',
        'App\\Providers\\EventServiceProvider' => __DIR__ . '/../..' . '/app/Providers/EventServiceProvider.php',
        'App\\Providers\\RouteServiceProvider' => __DIR__ . '/../..' . '/app/Providers/RouteServiceProvider.php',
        'App\\Repositories\\DormRepo' => __DIR__ . '/../..' . '/app/Repositories/DormRepo.php',
        'App\\Repositories\\ExamRepo' => __DIR__ . '/../..' . '/app/Repositories/ExamRepo.php',
        'App\\Repositories\\LocationRepo' => __DIR__ . '/../..' . '/app/Repositories/LocationRepo.php',
        'App\\Repositories\\MarkRepo' => __DIR__ . '/../..' . '/app/Repositories/MarkRepo.php',
        'App\\Repositories\\MyClassRepo' => __DIR__ . '/../..' . '/app/Repositories/MyClassRepo.php',
        'App\\Repositories\\PaymentRepo' => __DIR__ . '/../..' . '/app/Repositories/PaymentRepo.php',
        'App\\Repositories\\PinRepo' => __DIR__ . '/../..' . '/app/Repositories/PinRepo.php',
        'App\\Repositories\\SettingRepo' => __DIR__ . '/../..' . '/app/Repositories/SettingRepo.php',
        'App\\Repositories\\StudentRepo' => __DIR__ . '/../..' . '/app/Repositories/StudentRepo.php',
        'App\\Repositories\\TimeTableRepo' => __DIR__ . '/../..' . '/app/Repositories/TimeTableRepo.php',
        'App\\Repositories\\UserRepo' => __DIR__ . '/../..' . '/app/Repositories/UserRepo.php',
        'App\\User' => __DIR__ . '/../..' . '/app/User.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Database\\Factories\\StudentRecordFactory' => __DIR__ . '/../..' . '/database/factories/StudentRecordFactory.php',
        'Database\\Factories\\UserFactory' => __DIR__ . '/../..' . '/database/factories/UserFactory.php',
        'Database\\Seeders\\ClassTypesTableSeeder' => __DIR__ . '/../..' . '/database/seeders/ClassTypesTableSeeder.php',
        'Database\\Seeders\\DatabaseSeeder' => __DIR__ . '/../..' . '/database/seeders/DatabaseSeeder.php',
        'Database\\Seeders\\SettingsTableSeeder' => __DIR__ . '/../..' . '/database/seeders/SettingsTableSeeder.php',
        'Database\\Seeders\\UserTypesTableSeeder' => __DIR__ . '/../..' . '/database/seeders/UserTypesTableSeeder.php',
        'Database\\Seeders\\UsersTableSeeder' => __DIR__ . '/../..' . '/database/seeders/UsersTableSeeder.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitea995b8cb853bf0aff0cfe4706707e10::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitea995b8cb853bf0aff0cfe4706707e10::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitea995b8cb853bf0aff0cfe4706707e10::$classMap;

        }, null, ClassLoader::class);
    }
}
