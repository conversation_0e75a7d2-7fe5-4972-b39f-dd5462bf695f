[2025-07-19 18:37:00] local.ERROR: could not find driver (SQL: select * from `settings` where `type` = system_name limit 1) (View: C:\Users\<USER>\Documents\dev\CPA\CPA\resources\views\partials\login\header.blade.php) {"view":{"view":"C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","data":[]},"exception":"[object] (Facade\\Ignition\\Exceptions\\ViewException(code: 0): could not find driver (SQL: select * from `settings` where `type` = system_name limit 1) (View: C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views\\partials\\login\\header.blade.php) at C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2401}()
#5 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(261): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(278): App\\Helpers\\Qs::getSetting('system_name')
#11 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views/partials/login/header.blade.php(5): App\\Helpers\\Qs::getSystemName()
#12 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#14 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#19 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#20 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views/layouts/login_master.blade.php(16): Illuminate\\View\\View->render()
#21 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#23 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#28 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#29 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views/auth/login.blade.php(75): Illuminate\\View\\View->render()
#30 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#32 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#37 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#38 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#39 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#40 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#41 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#42 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():719}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#63 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():164}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\Database\\QueryException(code: 0): could not find driver (SQL: select * from `settings` where `type` = system_name limit 1) at C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2401}()
#5 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(261): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(278): App\\Helpers\\Qs::getSetting('system_name')
#11 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\b533bff2dccb6bbfd4799b58691e2ed9b9a74f5b.php(5): App\\Helpers\\Qs::getSystemName()
#12 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#14 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#19 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#20 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\bd1855daf219d7eceba9adbf4e065f9fb78e9913.php(16): Illuminate\\View\\View->render()
#21 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#23 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#28 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#29 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\9cb4a1ac6215b2c31e301178510e0d53f40d403e.php(75): Illuminate\\View\\View->render()
#30 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#32 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#37 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#38 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#39 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#40 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#41 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#42 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():719}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#63 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():164}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'cpa', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'cpa', 'cpa', Array)
#2 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():359}('select * from `...', Array)
#10 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2401}()
#15 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(261): Illuminate\\Database\\Eloquent\\Builder->first()
#20 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(278): App\\Helpers\\Qs::getSetting('system_name')
#21 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\b533bff2dccb6bbfd4799b58691e2ed9b9a74f5b.php(5): App\\Helpers\\Qs::getSystemName()
#22 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#24 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#29 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#30 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\bd1855daf219d7eceba9adbf4e065f9fb78e9913.php(16): Illuminate\\View\\View->render()
#31 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#33 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#38 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#39 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\9cb4a1ac6215b2c31e301178510e0d53f40d403e.php(75): Illuminate\\View\\View->render()
#40 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#42 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#47 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#48 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#49 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#50 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#51 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#52 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#53 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():719}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#73 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():164}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#85 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#87 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#89 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#91 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#92 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#93 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#94 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\server.php(21): require_once('C:\\\\Users\\\\<USER>\Users\WINDOWS 10\Documents\dev\CPA\CPA\resources\views\partials\login\header.blade.php) {"view":{"view":"C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","data":[]},"exception":"[object] (Facade\\Ignition\\Exceptions\\ViewException(code: 0): could not find driver (SQL: select * from `settings` where `type` = system_name limit 1) (View: C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views\\partials\\login\\header.blade.php) at C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2401}()
#5 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(261): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(278): App\\Helpers\\Qs::getSetting('system_name')
#11 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views/partials/login/header.blade.php(5): App\\Helpers\\Qs::getSystemName()
#12 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#14 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#19 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#20 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views/layouts/login_master.blade.php(16): Illuminate\\View\\View->render()
#21 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#23 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#28 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#29 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views/auth/login.blade.php(75): Illuminate\\View\\View->render()
#30 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#32 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#37 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#38 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#39 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#40 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#41 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#42 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():719}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#63 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():164}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\public\\index.php(68): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\Database\\QueryException(code: 0): could not find driver (SQL: select * from `settings` where `type` = system_name limit 1) at C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2401}()
#5 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(261): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(278): App\\Helpers\\Qs::getSetting('system_name')
#11 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\b533bff2dccb6bbfd4799b58691e2ed9b9a74f5b.php(5): App\\Helpers\\Qs::getSystemName()
#12 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#14 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#19 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#20 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\bd1855daf219d7eceba9adbf4e065f9fb78e9913.php(16): Illuminate\\View\\View->render()
#21 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#23 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#28 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#29 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\9cb4a1ac6215b2c31e301178510e0d53f40d403e.php(75): Illuminate\\View\\View->render()
#30 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#32 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#37 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#38 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#39 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#40 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#41 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#42 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():719}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#63 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():164}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\public\\index.php(68): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'cpa', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'cpa', 'cpa', Array)
#2 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():359}('select * from `...', Array)
#10 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2401}()
#15 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(261): Illuminate\\Database\\Eloquent\\Builder->first()
#20 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(278): App\\Helpers\\Qs::getSetting('system_name')
#21 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\b533bff2dccb6bbfd4799b58691e2ed9b9a74f5b.php(5): App\\Helpers\\Qs::getSystemName()
#22 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#24 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#29 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#30 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\bd1855daf219d7eceba9adbf4e065f9fb78e9913.php(16): Illuminate\\View\\View->render()
#31 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#33 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#38 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#39 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\9cb4a1ac6215b2c31e301178510e0d53f40d403e.php(75): Illuminate\\View\\View->render()
#40 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#42 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#47 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#48 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#49 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#50 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#51 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#52 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#53 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():719}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#73 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():164}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#85 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#87 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#89 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#91 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#92 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#93 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\public\\index.php(68): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#94 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\server.php(21): require_once('C:\\\\Users\\\\<USER>\Users\WINDOWS 10\Documents\dev\CPA\CPA\resources\views\partials\login\header.blade.php) {"view":{"view":"C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","data":[]},"exception":"[object] (Facade\\Ignition\\Exceptions\\ViewException(code: 0): could not find driver (SQL: select * from `settings` where `type` = system_name limit 1) (View: C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views\\partials\\login\\header.blade.php) at C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2401}()
#5 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(261): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(278): App\\Helpers\\Qs::getSetting('system_name')
#11 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views/partials/login/header.blade.php(5): App\\Helpers\\Qs::getSystemName()
#12 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#14 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#19 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#20 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views/layouts/login_master.blade.php(16): Illuminate\\View\\View->render()
#21 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#23 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#28 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#29 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\resources\\views/auth/login.blade.php(75): Illuminate\\View\\View->render()
#30 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#32 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#37 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#38 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#39 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#40 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#41 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#42 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():719}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#63 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():164}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\public\\index.php(68): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from `settings` where `type` = system_name limit 1) at C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2401}()
#5 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(261): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(278): App\\Helpers\\Qs::getSetting('system_name')
#11 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\b533bff2dccb6bbfd4799b58691e2ed9b9a74f5b.php(5): App\\Helpers\\Qs::getSystemName()
#12 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#14 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#19 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#20 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\bd1855daf219d7eceba9adbf4e065f9fb78e9913.php(16): Illuminate\\View\\View->render()
#21 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#23 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#28 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#29 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\9cb4a1ac6215b2c31e301178510e0d53f40d403e.php(75): Illuminate\\View\\View->render()
#30 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#32 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#37 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#38 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#39 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#40 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#41 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#42 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():719}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#63 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():164}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\public\\index.php(68): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'cpa', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'cpa', 'cpa', Array)
#2 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():359}('select * from `...', Array)
#10 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2401}()
#15 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(261): Illuminate\\Database\\Eloquent\\Builder->first()
#20 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Helpers\\Qs.php(278): App\\Helpers\\Qs::getSetting('system_name')
#21 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\b533bff2dccb6bbfd4799b58691e2ed9b9a74f5b.php(5): App\\Helpers\\Qs::getSystemName()
#22 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#24 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#29 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#30 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\bd1855daf219d7eceba9adbf4e065f9fb78e9913.php(16): Illuminate\\View\\View->render()
#31 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#33 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#38 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#39 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\storage\\framework\\views\\9cb4a1ac6215b2c31e301178510e0d53f40d403e.php(75): Illuminate\\View\\View->render()
#40 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():104}()
#42 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\WINDOWS 10\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#47 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#48 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#49 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#50 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#51 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#52 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#53 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():719}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#73 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():164}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():126}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#85 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#87 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#89 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():142}:143}(Object(Illuminate\\Http\\Request))
#91 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#92 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#93 C:\\Users\\<USER>\\Documents\\dev\\CPA\\CPA\\public\\index.php(68): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#94 {main}
"} 
